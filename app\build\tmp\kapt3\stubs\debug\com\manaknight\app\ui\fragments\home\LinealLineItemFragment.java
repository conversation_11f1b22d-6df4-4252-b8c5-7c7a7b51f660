package com.manaknight.app.ui.fragments.home;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000v\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010%\u001a\u00020&H\u0002J(\u0010\'\u001a\u00020&2\u0006\u0010(\u001a\u00020)2\u0006\u0010*\u001a\u00020)2\u0006\u0010+\u001a\u00020)2\u0006\u0010,\u001a\u00020-H\u0002J(\u0010.\u001a\u00020&2\u0006\u0010(\u001a\u00020)2\u0006\u0010*\u001a\u00020)2\u0006\u0010+\u001a\u00020)2\u0006\u0010,\u001a\u00020-H\u0002J\b\u0010/\u001a\u00020&H\u0002J\b\u00100\u001a\u00020&H\u0016J\b\u00101\u001a\u00020&H\u0016J\u0018\u00102\u001a\u00020&2\u0006\u00103\u001a\u00020\u001e2\u0006\u00104\u001a\u000205H\u0002J\b\u00106\u001a\u00020&H\u0016J\u001a\u00107\u001a\u00020&2\u0006\u00108\u001a\u0002092\b\u0010:\u001a\u0004\u0018\u00010;H\u0016J\b\u0010<\u001a\u00020&H\u0002J\b\u0010=\u001a\u00020&H\u0002J\u0006\u0010>\u001a\u00020&J\b\u0010?\u001a\u00020&H\u0002J\b\u0010@\u001a\u00020&H\u0002R\u001b\u0010\u0003\u001a\u00020\u00048BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\u0007\u0010\b\u001a\u0004\b\u0005\u0010\u0006R\u001b\u0010\t\u001a\u00020\n8BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\r\u0010\u000e\u001a\u0004\b\u000b\u0010\fR\u001b\u0010\u000f\u001a\u00020\u00108BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\u0013\u0010\u0014\u001a\u0004\b\u0011\u0010\u0012R\u000e\u0010\u0015\u001a\u00020\u0016X\u0082.\u00a2\u0006\u0002\n\u0000R\u001b\u0010\u0017\u001a\u00020\u00188BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\u001b\u0010\u000e\u001a\u0004\b\u0019\u0010\u001aR\u001e\u0010\u001c\u001a\u0012\u0012\u0004\u0012\u00020\u001e0\u001dj\b\u0012\u0004\u0012\u00020\u001e`\u001fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001b\u0010 \u001a\u00020!8BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b$\u0010\u000e\u001a\u0004\b\"\u0010#\u00a8\u0006A"}, d2 = {"Lcom/manaknight/app/ui/fragments/home/<USER>", "Landroidx/fragment/app/Fragment;", "()V", "args", "Lcom/manaknight/app/ui/fragments/home/<USER>", "getArgs", "()Lcom/manaknight/app/ui/fragments/home/<USER>", "args$delegate", "Landroidx/navigation/NavArgsLazy;", "baasViewModel", "Lcom/manaknight/app/viewmodels/BaasViewModel;", "getBaasViewModel", "()Lcom/manaknight/app/viewmodels/BaasViewModel;", "baasViewModel$delegate", "Lkotlin/Lazy;", "binding", "LManaknight/databinding/FragmentLinearLineItemBinding;", "getBinding", "()LManaknight/databinding/FragmentLinearLineItemBinding;", "binding$delegate", "Lcom/manaknight/app/extensions/FragmentDelegate;", "dialog", "Landroid/app/Dialog;", "linearAdapter", "Lcom/manaknight/app/adapter/LinearLineAdapter;", "getLinearAdapter", "()Lcom/manaknight/app/adapter/LinearLineAdapter;", "linearAdapter$delegate", "linearList", "Ljava/util/ArrayList;", "Lcom/manaknight/app/model/remote/profitPro/LinearRespListModel;", "Lkotlin/collections/ArrayList;", "pref", "Lcom/manaknight/app/data/local/AppPreferences;", "getPref", "()Lcom/manaknight/app/data/local/AppPreferences;", "pref$delegate", "addLinearLineItem", "", "addNewLinearItem", "cost", "", "laborCost", "name", "sheet", "Lcom/google/android/material/bottomsheet/BottomSheetDialog;", "addNewSquareItem", "getLinearList", "onPause", "onResume", "onSelectMaterial", "item", "position", "", "onStop", "onViewCreated", "view", "Landroid/view/View;", "savedInstanceState", "Landroid/os/Bundle;", "showAddLineal", "showAddSqaure", "updateButtonState", "updateLinearLineItem", "updateProjectStatusToDraft", "app_debug"})
public final class LinealLineItemFragment extends androidx.fragment.app.Fragment {
    @org.jetbrains.annotations.NotNull()
    private final com.manaknight.app.extensions.FragmentDelegate binding$delegate = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.navigation.NavArgsLazy args$delegate = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy baasViewModel$delegate = null;
    private android.app.Dialog dialog;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy pref$delegate = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy linearAdapter$delegate = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.ArrayList<com.manaknight.app.model.remote.profitPro.LinearRespListModel> linearList = null;
    
    public LinealLineItemFragment() {
        super();
    }
    
    private final Manaknight.databinding.FragmentLinearLineItemBinding getBinding() {
        return null;
    }
    
    private final com.manaknight.app.ui.fragments.home.LinealLineItemFragmentArgs getArgs() {
        return null;
    }
    
    private final com.manaknight.app.viewmodels.BaasViewModel getBaasViewModel() {
        return null;
    }
    
    private final com.manaknight.app.data.local.AppPreferences getPref() {
        return null;
    }
    
    private final com.manaknight.app.adapter.LinearLineAdapter getLinearAdapter() {
        return null;
    }
    
    private final void onSelectMaterial(com.manaknight.app.model.remote.profitPro.LinearRespListModel item, int position) {
    }
    
    public final void updateButtonState() {
    }
    
    @java.lang.Override()
    public void onViewCreated(@org.jetbrains.annotations.NotNull()
    android.view.View view, @org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    private final void getLinearList() {
    }
    
    private final void addNewLinearItem(java.lang.String cost, java.lang.String laborCost, java.lang.String name, com.google.android.material.bottomsheet.BottomSheetDialog sheet) {
    }
    
    private final void addNewSquareItem(java.lang.String cost, java.lang.String laborCost, java.lang.String name, com.google.android.material.bottomsheet.BottomSheetDialog sheet) {
    }
    
    private final void addLinearLineItem() {
    }
    
    private final void updateLinearLineItem() {
    }
    
    private final void showAddLineal() {
    }
    
    private final void updateProjectStatusToDraft() {
    }
    
    private final void showAddSqaure() {
    }
    
    @java.lang.Override()
    public void onPause() {
    }
    
    @java.lang.Override()
    public void onResume() {
    }
    
    @java.lang.Override()
    public void onStop() {
    }
}