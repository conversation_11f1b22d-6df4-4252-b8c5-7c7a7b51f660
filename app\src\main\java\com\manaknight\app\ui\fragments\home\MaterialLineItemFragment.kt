
package com.manaknight.app.ui.fragments.home

import android.os.Bundle
import android.view.View
import androidx.core.widget.doAfterTextChanged
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import android.widget.Toast
import Manaknight.R
import Manaknight.databinding.BottomAddEmployeeBinding
import Manaknight.databinding.BottomAddMaterialBinding
import Manaknight.databinding.FragmentCreateCustomerBinding
import Manaknight.databinding.FragmentLineItemsBinding
import Manaknight.databinding.FragmentMaterialLineItemBinding
import Manaknight.databinding.FragmentSignUpBinding
import android.app.Activity
import android.app.Dialog
import android.content.ContentValues.TAG
import android.text.Editable
import android.text.TextWatcher
import android.util.Log
import android.view.MotionEvent
import android.widget.EditText
import android.graphics.Rect

import android.view.ViewGroup
import android.view.WindowManager
import android.view.inputmethod.InputMethodManager

import android.widget.RelativeLayout
import com.manaknight.app.extensions.checkIsEmpty
import com.manaknight.app.extensions.disableSpaces
import com.manaknight.app.extensions.hide
import com.manaknight.app.extensions.hideSoftKeyboard
import com.manaknight.app.extensions.setOnClickWithDebounce
import com.manaknight.app.extensions.show
import com.manaknight.app.extensions.snackBar
import com.manaknight.app.extensions.textToString
import com.manaknight.app.extensions.viewBinding
import com.manaknight.app.network.Status
import com.manaknight.app.viewmodels.BaasViewModel
import org.koin.androidx.viewmodel.ext.android.viewModel
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.LiveData
import androidx.navigation.NavOptions
import androidx.navigation.fragment.navArgs
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.manaknight.app.adapter.CustomerAdapter
import com.manaknight.app.adapter.MaterialLineAdapter
import com.manaknight.app.data.local.AppPreferences
import com.manaknight.app.extensions.invisible
import com.manaknight.app.extensions.isEmailValid
import com.manaknight.app.extensions.setVerticalLayout
import com.manaknight.app.extensions.showProgressBar
import com.manaknight.app.model.remote.EmplyeeModel
import com.manaknight.app.model.remote.profitPro.CommonResponse
import com.manaknight.app.model.remote.profitPro.CompanyRequest
import com.manaknight.app.model.remote.profitPro.CreateLineEntriesReqModel
import com.manaknight.app.model.remote.profitPro.CreateLineItemReqModel
import com.manaknight.app.model.remote.profitPro.CustomerRespListModel
import com.manaknight.app.model.remote.profitPro.MaterialRespListModel
import com.manaknight.app.model.remote.profitPro.UpdateLineEntriesReqModel
import com.manaknight.app.model.remote.profitPro.UpdateLineItemReqModel
import com.manaknight.app.network.Resource
import com.manaknight.app.ui.CreateEstimationFragment
import com.manaknight.app.ui.CreateEstimationFragmentDirections
import com.manaknight.app.utils.ProgressDialog.Companion.progressDialog
import org.koin.android.ext.android.inject

class MaterialLineItemFragment : Fragment(R.layout.fragment_material_line_item) {

    private val binding by viewBinding(FragmentMaterialLineItemBinding::bind)
    private val args by navArgs<MaterialLineItemFragmentArgs>()
    private val baasViewModel: BaasViewModel by viewModel()

    private lateinit var dialog: Dialog
    private val pref by inject<AppPreferences>()

    private val materialAdapter by lazy { MaterialLineAdapter(this::onSelectMaterial) }

    private val materialList: ArrayList<MaterialRespListModel> = ArrayList()


    private fun onSelectMaterial(item: MaterialRespListModel, position: Int) {
        Log.d(TAG, "onAlertClick: $position")

        updateButtonState()
    }

    fun updateButtonState() {
        val isMaterialSelected = materialAdapter.list.any { it.isSelected == true && (it.units ?: 0) > 0 }
        //val isFullNameNotEmpty = (args.isEditable == 1) ? true : binding.edTxtFullName.text.isNotEmpty()
        val isFullNameNotEmpty = if (args.isEditable == 1) {
            true
        } else {
            binding.edTxtFullName.text.isNotEmpty()
        }


        val isEnabled = isMaterialSelected && isFullNameNotEmpty

        binding.headerInclude.btnSave.isEnabled = isEnabled
        binding.headerInclude.btnSave.alpha = if (isEnabled) 1f else 0.5f  // Adjust transparency

    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        dialog = progressDialog(requireContext())
        binding.headerInclude.backButton.setOnClickListener {
            findNavController().popBackStack()
        }

        binding.headerInclude.btnSave.setOnClickListener {
            if (args.isEditable == 1) {
                updateMaterialLineItem()
            } else {
                addMaterialLineItem()
            }
        }

        binding.headerInclude.btnSave.isEnabled = false
        binding.headerInclude.btnSave.alpha = 0.5f

        binding.headerInclude.addPlaterTitle.text =  "New Line Item #" + args.lineItem

        binding.btnAddLineItem.setOnClickListener {
            showAddEmployee()
        }

        // Add touch listener to handle focus clearing when clicking outside EditText
        binding.root.setOnTouchListener { _, event ->
            if (event.action == MotionEvent.ACTION_DOWN) {
                val currentFocus = activity?.currentFocus
                if (currentFocus is EditText) {
                    val outRect = Rect()
                    currentFocus.getGlobalVisibleRect(outRect)
                    if (!outRect.contains(event.rawX.toInt(), event.rawY.toInt())) {
                        currentFocus.clearFocus()
                        requireActivity().hideSoftKeyboard()
                    }
                }
            }
            false // Allow other touch events to be processed
        }

        binding.linealRecylerView.apply {
            setVerticalLayout()
            adapter = materialAdapter
        }

        binding.edTxtFullName.doAfterTextChanged { updateButtonState() }

        if(args.isEditable == 1) {
            //binding.txtFullName.visibility = View.GONE
            //binding.edTxtFullName.visibility = View.GONE
            binding.headerInclude.addPlaterTitle.text =  "Update Line Item"

            binding.headerInclude.btnSave.isEnabled = true
            binding.headerInclude.btnSave.alpha = 1.0f

            binding.edTxtFullName.setText(args.labourHours)

        }
    }

    private fun getMaterialList() {

        baasViewModel.getDefaultMaterialList(filter = "user_id,cs,${pref.userId}")
            .observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> {
                    snackBar(it.message ?: "Server Error")
                    dialog.dismiss()
                }
                Status.LOADING -> {
                    dialog.show()
                }

                Status.SUCCESS -> {
                    dialog.dismiss()

                    materialList.clear()
                    it.data?.list?.let { it1 ->
                        materialList.addAll(it1)

                        if (args.isEditable == 1) {

                            materialList.forEach { material ->
                                val matchingItem = args.materialItem.list?.find { newItem -> newItem.name == material.name }
                                if (matchingItem != null) {
                                    material.isSelected = true
                                    material.units = matchingItem.quantity // Set units from quantity
                                    material.selectedID = matchingItem.id
                                }
                            }
                        }
                    }

                    materialAdapter.refresh(materialList)
                }
            }
        }
    }

    private fun addNewMaterial(name: String, cost: String, sheet: BottomSheetDialog) {

        baasViewModel.createDefaultMaterial(pref.userId, cost.toIntOrNull() ?: 0, name)
            .observe(viewLifecycleOwner) {
                when (it.status) {
                    Status.ERROR -> {
                        snackBar(it.message ?: "Server Error")
                        dialog.dismiss()
                    }
                    Status.LOADING -> {
                        dialog.show()
                    }

                    Status.SUCCESS -> {
                        dialog.dismiss()
                        sheet.dismiss()
                        getMaterialList()

                    }
                }
            }
    }

    private fun addMaterialLineItem() {

        val selectedMaterials = materialAdapter.list
            .filter { it.isSelected == true && (it.units ?: 0) > 0 } // Filter valid items
            .map { material ->
                CreateLineEntriesReqModel(
                    quantity = material.units ?: 0, // Safe null check
                    item_id = material.id ?: 0     // Safe null check
                )
            }

        baasViewModel.addLineItem(CreateLineItemReqModel(args.lineDescrition, args.lineEstimateType, args.projectID, binding.edTxtFullName.text.toString().toIntOrNull() ?: 0, selectedMaterials))
            .observe(viewLifecycleOwner) {
                when (it.status) {
                    Status.ERROR -> {
                        snackBar(it.message ?: "Server Error")
                        dialog.dismiss()
                    }
                    Status.LOADING -> {
                        dialog.show()
                    }

                    Status.SUCCESS -> {
                        dialog.dismiss()
                        
                        // Update project status to draft
                        updateProjectStatusToDraft()

                        findNavController().popBackStack(R.id.lineItemView, false)

//                        findNavController().navigate(
//                            R.id.lineItemView,
//                            null,
//                            NavOptions.Builder()
//                                .setPopUpTo(R.id.addLineItemFragment, true) // Removes intermediate fragments
//                                .build()
//                        )
                    }
                }
            }
    }

    private fun updateMaterialLineItem() {

        val selectedMaterials = materialAdapter.list
            .filter { it.isSelected == true && (it.units ?: 0) > 0 } // Filter valid items
            .map { material ->
                UpdateLineEntriesReqModel(
                    quantity = material.units ?: 0, // Safe null check
                    item_id = material.id ?: 0,
                    material.selectedID,
                    material.name.toString()
                )
            }

        baasViewModel.updateLineItem(args.itemLineID, UpdateLineItemReqModel(
            args.lineDescrition,
            args.lineEstimateType,
            args.projectID,
            binding.edTxtFullName.text.toString().toIntOrNull() ?: 0,
            selectedMaterials
        ))
            .observe(viewLifecycleOwner) {
                when (it.status) {
                    Status.ERROR -> {
                        snackBar(it.message ?: "Server Error")
                        dialog.dismiss()
                    }
                    Status.LOADING -> {
                        dialog.show()
                    }

                    Status.SUCCESS -> {
                        dialog.dismiss()
                        
                        // Update project status to draft
                        updateProjectStatusToDraft()

                        findNavController().popBackStack(R.id.lineItemView, false)

//                        findNavController().navigate(
//                            R.id.lineItemView,
//                            null,
//                            NavOptions.Builder()
//                                .setPopUpTo(R.id.addLineItemFragment, true) // Removes intermediate fragments
//                                .build()
//                        )
                    }
                }
            }
    }

    private fun updateProjectStatusToDraft() {
        // Get project details to retrieve necessary data for updateProject
        baasViewModel.getSingleProjectDetails(args.projectID).observe(viewLifecycleOwner) { projectResource ->
            if (projectResource.status == Status.SUCCESS && projectResource.data?.error == false) {
                val clientDetails = projectResource.data?.client_details
                val totals = projectResource.data?.totals
                val changeCount = 0
                val customerId = clientDetails?.customer_id ?: 0
                val userId = clientDetails?.user_id ?: 0
                val profitOverhead = totals?.total_profit_overhead?.toString() ?: "0"
                val hourlyRate = totals?.labour_budget?.toString() ?: "0"

                baasViewModel.updateProject(
                    changeCount = changeCount,
                    customerId = customerId,
                    userId = userId,
                    status = 3, // draft
                    profitOverhead = profitOverhead,
                    hourlyRate = hourlyRate,
                    id = args.projectID
                ).observe(viewLifecycleOwner) { updateResource ->
                    when (updateResource.status) {
                        Status.LOADING -> {
                            // Show loading if needed
                        }
                        Status.SUCCESS -> {
                            if (updateResource.data?.error == false) {
                                Log.d("MaterialLineItemFragment", "Project status updated to Draft (3)")
                            }
                        }
                        Status.ERROR -> {
                            Log.e("MaterialLineItemFragment", "Failed to update project status: ${updateResource.message}")
                        }
                    }
                }
            }
        }
    }

    private fun showAddEmployee() {

        val sheet = BottomSheetDialog(requireContext(), R.style.BottomSheetDialogTheme)
        val view = BottomAddMaterialBinding.inflate(layoutInflater)

        sheet.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_NOTHING)
        sheet.setOnShowListener { dialogInterface ->
            val bottomSheetDialog = dialogInterface as BottomSheetDialog
            val bottomSheet = bottomSheetDialog.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet)

            if (bottomSheet != null) {
                // Set height to full screen
                bottomSheet.layoutParams.height = ViewGroup.LayoutParams.MATCH_PARENT

                // Add a top margin of 100px
                val layoutParams = bottomSheet.layoutParams as ViewGroup.MarginLayoutParams
                layoutParams.topMargin = 0 // Adjust margin as needed
                layoutParams.bottomMargin = 0
                bottomSheet.layoutParams = layoutParams

                // Ensure the bottom sheet is expanded
                val behavior = BottomSheetBehavior.from(bottomSheet)
                behavior.state = BottomSheetBehavior.STATE_EXPANDED
                behavior.isFitToContents = false
                behavior.skipCollapsed = true
            }

        }
        view.apply {

            btnSave.isEnabled = false
            btnSave.alpha = 0.5f
            checkBox1.visibility = View.VISIBLE

            val textWatcher = object : TextWatcher {
                override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

                override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                    // Enable Save button if both fields are non-empty, otherwise disable it
                    btnSave.isEnabled = edTxtMaterialName.text.isNotEmpty() && edTxtUnitCost.text.isNotEmpty()
                    btnSave.alpha = if (btnSave.isEnabled) 1f else 0.5f
                }

                override fun afterTextChanged(s: Editable?) {}
            }

            edTxtMaterialName.addTextChangedListener(textWatcher)
            edTxtUnitCost.addTextChangedListener(textWatcher)

            backButton.setOnClickListener {
                sheet.dismiss()
            }
            btnSave.setOnClickListener {


                if(edTxtMaterialName.text.toString().isEmpty()) {
                    Toast.makeText(context, "Please enter material name", Toast.LENGTH_SHORT).show();
                } else if(edTxtUnitCost.text.isEmpty()) {
                    Toast.makeText(context, "Please enter unit cost", Toast.LENGTH_SHORT).show();
                } else {

                    addNewMaterial(edTxtMaterialName.text.toString(), edTxtUnitCost.text.toString(), sheet)
                }
            }

            line1.setOnClickListener() {
                val imm = requireActivity().getSystemService(Activity.INPUT_METHOD_SERVICE) as InputMethodManager
                imm.hideSoftInputFromWindow(view.root.windowToken, 0)

            }


        }


        sheet.setOnDismissListener {
            requireActivity().hideSoftKeyboard()
        }

        sheet.setCancelable(true)
        sheet.setContentView(view.root)
        sheet.show()
    }

    override fun onPause() {
        super.onPause()
        binding.headerInclude.btnSave.invisible()
    }
  override fun onResume() {
            super.onResume()
            (activity as AppCompatActivity?)?.supportActionBar?.hide()
      binding.headerInclude.backButton.show()
      binding.headerInclude.btnSave.show()
      getMaterialList()
        }

        override fun onStop() {
            super.onStop()
            (activity as AppCompatActivity?)?.supportActionBar?.show()
        }

}
    
