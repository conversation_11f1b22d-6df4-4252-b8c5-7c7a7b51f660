package com.manaknight.app.ui.screens;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000d\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\u001a\u00c5\u0001\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00032\u0006\u0010\u0005\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\u00072\b\u0010\t\u001a\u0004\u0018\u00010\u00072\b\u0010\n\u001a\u0004\u0018\u00010\u000b2\b\u0010\f\u001a\u0004\u0018\u00010\u00032\u0006\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u00102\u0012\u0010\u0011\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00140\u00130\u00122\u000e\u0010\u0015\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00030\u00122\f\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u00070\u00122\u0006\u0010\u0017\u001a\u00020\u00182\u0012\u0010\u0019\u001a\u000e\u0012\u0004\u0012\u00020\u0018\u0012\u0004\u0012\u00020\u00010\u001a2\b\b\u0002\u0010\u001b\u001a\u00020\u00182\b\b\u0002\u0010\u001c\u001a\u00020\u0018H\u0003\u00a2\u0006\u0002\u0010\u001d\u001ak\u0010\u001e\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00032\u0006\u0010\u0005\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\u00072\b\u0010\t\u001a\u0004\u0018\u00010\u00072\b\u0010\n\u001a\u0004\u0018\u00010\u000b2\b\u0010\f\u001a\u0004\u0018\u00010\u00032\u0006\u0010\u001f\u001a\u00020\u00072\u0006\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u0010H\u0007\u00a2\u0006\u0002\u0010 \u001a \u0010!\u001a\u00020\u00012\u0006\u0010\u001f\u001a\u00020\u00072\u0006\u0010\u0004\u001a\u00020\u00032\u0006\u0010\r\u001a\u00020\u000eH\u0003\u001a4\u0010\"\u001a\u00020\u00012\f\u0010#\u001a\b\u0012\u0004\u0012\u00020\u00010$2\f\u0010%\u001a\b\u0012\u0004\u0012\u00020\u00010$2\u0006\u0010\b\u001a\u00020\u00072\u0006\u0010\u000f\u001a\u00020\u0010H\u0007\u001a4\u0010&\u001a\u00020\u00012\f\u0010#\u001a\b\u0012\u0004\u0012\u00020\u00010$2\f\u0010%\u001a\b\u0012\u0004\u0012\u00020\u00010$2\u0006\u0010\b\u001a\u00020\u00072\u0006\u0010\u000f\u001a\u00020\u0010H\u0007\u001a\u001e\u0010\'\u001a\u00020\u00012\u0006\u0010\b\u001a\u00020\u00072\f\u0010(\u001a\b\u0012\u0004\u0012\u00020\u00010$H\u0007\u001a$\u0010)\u001a\u00020\u00012\u0006\u0010*\u001a\u00020\u00182\u0012\u0010+\u001a\u000e\u0012\u0004\u0012\u00020\u0018\u0012\u0004\u0012\u00020\u00010\u001aH\u0007\u001a:\u0010,\u001a\u00020\u00012\f\u0010-\u001a\b\u0012\u0004\u0012\u00020/0.2\f\u0010%\u001a\b\u0012\u0004\u0012\u00020\u00010$2\f\u0010#\u001a\b\u0012\u0004\u0012\u00020\u00010$2\u0006\u00100\u001a\u000201H\u0002\u001a]\u00102\u001a\u00020\u00012\u0006\u0010\u0004\u001a\u00020\u00032\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\u00072\u0006\u0010\t\u001a\u00020\u00032\u0006\u00103\u001a\u00020\u00142\u0006\u00104\u001a\u00020\u00032\b\u0010\f\u001a\u0004\u0018\u00010\u00032\u0006\u0010\u000f\u001a\u00020\u00102\u0006\u0010\r\u001a\u00020\u000e\u00a2\u0006\u0002\u00105\u001a\u0018\u00106\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u000f\u001a\u00020\u0010H\u0002\u00a8\u00067"}, d2 = {"AddEditLinealLineItemDetailContent", "", "projectID", "", "isEditable", "lineItemNumber", "lineDescription", "", "lineEstimateType", "labourHours", "materialItem", "Lcom/manaknight/app/model/remote/profitPro/MaterialRespListModel2;", "itemLineID", "navController", "Landroidx/navigation/NavController;", "baasViewModel", "Lcom/manaknight/app/viewmodels/BaasViewModel;", "linealListState", "Landroidx/compose/runtime/MutableState;", "", "Lcom/manaknight/app/model/remote/profitPro/LinearRespListModel;", "selectedLinearItemId", "unitsState", "showAddNewCostSheet", "", "onShowAddNewCostSheetChange", "Lkotlin/Function1;", "showTopBar", "showDetailHeader", "(IIILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Lcom/manaknight/app/model/remote/profitPro/MaterialRespListModel2;Ljava/lang/Integer;Landroidx/navigation/NavController;Lcom/manaknight/app/viewmodels/BaasViewModel;Landroidx/compose/runtime/MutableState;Landroidx/compose/runtime/MutableState;Landroidx/compose/runtime/MutableState;ZLkotlin/jvm/functions/Function1;ZZ)V", "AddEditLinealLineItemScreen", "customerName", "(IIILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Lcom/manaknight/app/model/remote/profitPro/MaterialRespListModel2;Ljava/lang/Integer;Ljava/lang/String;Landroidx/navigation/NavController;Lcom/manaknight/app/viewmodels/BaasViewModel;)V", "AddEditLinealLineItemTopBar", "AddNewLinearOrSquareCostDialog", "onDismissRequest", "Lkotlin/Function0;", "onCostAdded", "AddNewLinearOrSquareCostDialogContent", "AddNewLinearOrSquareCostDialogHeader", "onDismiss", "CustomCheckboxRow", "isChecked", "onCheckedChange", "handleCostAdditionResponse", "response", "Lcom/manaknight/app/network/Resource;", "Lcom/manaknight/app/model/remote/profitPro/CommonResponse;", "context", "Landroid/content/Context;", "saveLinealLineItem", "selectedItem", "units", "(IILjava/lang/String;Ljava/lang/String;ILcom/manaknight/app/model/remote/profitPro/LinearRespListModel;ILjava/lang/Integer;Lcom/manaknight/app/viewmodels/BaasViewModel;Landroidx/navigation/NavController;)V", "updateProjectStatusToDraft", "app_debug"})
public final class AddEditLinealLineItemScreenKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void AddEditLinealLineItemScreen(int projectID, int isEditable, int lineItemNumber, @org.jetbrains.annotations.NotNull()
    java.lang.String lineDescription, @org.jetbrains.annotations.NotNull()
    java.lang.String lineEstimateType, @org.jetbrains.annotations.Nullable()
    java.lang.String labourHours, @org.jetbrains.annotations.Nullable()
    com.manaknight.app.model.remote.profitPro.MaterialRespListModel2 materialItem, @org.jetbrains.annotations.Nullable()
    java.lang.Integer itemLineID, @org.jetbrains.annotations.NotNull()
    java.lang.String customerName, @org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController navController, @org.jetbrains.annotations.NotNull()
    com.manaknight.app.viewmodels.BaasViewModel baasViewModel) {
    }
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    private static final void AddEditLinealLineItemTopBar(java.lang.String customerName, int isEditable, androidx.navigation.NavController navController) {
    }
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    private static final void AddEditLinealLineItemDetailContent(int projectID, int isEditable, int lineItemNumber, java.lang.String lineDescription, java.lang.String lineEstimateType, java.lang.String labourHours, com.manaknight.app.model.remote.profitPro.MaterialRespListModel2 materialItem, java.lang.Integer itemLineID, androidx.navigation.NavController navController, com.manaknight.app.viewmodels.BaasViewModel baasViewModel, androidx.compose.runtime.MutableState<java.util.List<com.manaknight.app.model.remote.profitPro.LinearRespListModel>> linealListState, androidx.compose.runtime.MutableState<java.lang.Integer> selectedLinearItemId, androidx.compose.runtime.MutableState<java.lang.String> unitsState, boolean showAddNewCostSheet, kotlin.jvm.functions.Function1<? super java.lang.Boolean, kotlin.Unit> onShowAddNewCostSheetChange, boolean showTopBar, boolean showDetailHeader) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void AddNewLinearOrSquareCostDialog(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismissRequest, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onCostAdded, @org.jetbrains.annotations.NotNull()
    java.lang.String lineEstimateType, @org.jetbrains.annotations.NotNull()
    com.manaknight.app.viewmodels.BaasViewModel baasViewModel) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void AddNewLinearOrSquareCostDialogHeader(@org.jetbrains.annotations.NotNull()
    java.lang.String lineEstimateType, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void AddNewLinearOrSquareCostDialogContent(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismissRequest, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onCostAdded, @org.jetbrains.annotations.NotNull()
    java.lang.String lineEstimateType, @org.jetbrains.annotations.NotNull()
    com.manaknight.app.viewmodels.BaasViewModel baasViewModel) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void CustomCheckboxRow(boolean isChecked, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Boolean, kotlin.Unit> onCheckedChange) {
    }
    
    private static final void handleCostAdditionResponse(com.manaknight.app.network.Resource<com.manaknight.app.model.remote.profitPro.CommonResponse> response, kotlin.jvm.functions.Function0<kotlin.Unit> onCostAdded, kotlin.jvm.functions.Function0<kotlin.Unit> onDismissRequest, android.content.Context context) {
    }
    
    public static final void saveLinealLineItem(int isEditable, int projectID, @org.jetbrains.annotations.NotNull()
    java.lang.String lineDescription, @org.jetbrains.annotations.NotNull()
    java.lang.String lineEstimateType, int labourHours, @org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.profitPro.LinearRespListModel selectedItem, int units, @org.jetbrains.annotations.Nullable()
    java.lang.Integer itemLineID, @org.jetbrains.annotations.NotNull()
    com.manaknight.app.viewmodels.BaasViewModel baasViewModel, @org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController navController) {
    }
    
    private static final void updateProjectStatusToDraft(int projectID, com.manaknight.app.viewmodels.BaasViewModel baasViewModel) {
    }
}