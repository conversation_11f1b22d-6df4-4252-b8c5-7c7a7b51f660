
package com.manaknight.app.ui.fragments.home

import android.os.Bundle
import android.view.View
import androidx.core.widget.doAfterTextChanged
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import android.widget.Toast
import Manaknight.R
import Manaknight.databinding.BottomAddEmployeeBinding
import Manaknight.databinding.BottomAddLinealBinding
import Manaknight.databinding.FragmentCreateCustomerBinding
import Manaknight.databinding.FragmentLineItemsBinding
import Manaknight.databinding.FragmentLinearLineItemBinding
import Manaknight.databinding.FragmentMaterialLineItemBinding
import Manaknight.databinding.FragmentSignUpBinding
import android.app.Activity
import android.app.Dialog
import android.content.ContentValues.TAG
import android.text.Editable
import android.text.TextWatcher
import android.util.Log
import android.view.MotionEvent
import android.view.ViewGroup
import android.view.WindowManager
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import android.widget.RelativeLayout
import com.manaknight.app.extensions.checkIsEmpty
import com.manaknight.app.extensions.disableSpaces
import com.manaknight.app.extensions.hide
import com.manaknight.app.extensions.hideSoftKeyboard
import com.manaknight.app.extensions.setOnClickWithDebounce
import com.manaknight.app.extensions.show
import com.manaknight.app.extensions.snackBar
import com.manaknight.app.extensions.textToString
import com.manaknight.app.extensions.viewBinding
import com.manaknight.app.network.Status
import com.manaknight.app.viewmodels.BaasViewModel
import org.koin.androidx.viewmodel.ext.android.viewModel
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.LiveData
import androidx.navigation.NavOptions
import androidx.navigation.fragment.navArgs
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.manaknight.app.adapter.CustomerAdapter
import com.manaknight.app.adapter.LinearLineAdapter
import com.manaknight.app.adapter.MaterialLineAdapter
import com.manaknight.app.data.local.AppPreferences
import com.manaknight.app.extensions.invisible
import com.manaknight.app.extensions.isEmailValid
import com.manaknight.app.extensions.setVerticalLayout
import com.manaknight.app.extensions.showProgressBar
import com.manaknight.app.model.remote.EmplyeeModel
import com.manaknight.app.model.remote.LinealModel
import com.manaknight.app.model.remote.profitPro.CommonResponse
import com.manaknight.app.model.remote.profitPro.CompanyRequest
import com.manaknight.app.model.remote.profitPro.CreateLineEntriesReqModel
import com.manaknight.app.model.remote.profitPro.CreateLineItemReqModel
import com.manaknight.app.model.remote.profitPro.CustomerRespListModel
import com.manaknight.app.model.remote.profitPro.LinearRespListModel
import com.manaknight.app.model.remote.profitPro.MaterialRespListModel
import com.manaknight.app.model.remote.profitPro.UpdateLineEntriesReqModel
import com.manaknight.app.model.remote.profitPro.UpdateLineItemReqModel
import com.manaknight.app.network.Resource
import com.manaknight.app.ui.CreateEstimationFragment
import com.manaknight.app.ui.CreateEstimationFragmentDirections
import com.manaknight.app.utils.ProgressDialog.Companion.progressDialog
import org.koin.android.ext.android.inject

class LinealLineItemFragment : Fragment(R.layout.fragment_linear_line_item) {

    private val binding by viewBinding(FragmentLinearLineItemBinding::bind)
    private val args by navArgs<LinealLineItemFragmentArgs>()
    private val baasViewModel: BaasViewModel by viewModel()

    private lateinit var dialog: Dialog
    private val pref by inject<AppPreferences>()

    private val linearAdapter by lazy { LinearLineAdapter(args.lineEstimateType, this::onSelectMaterial) }

    private val linearList: ArrayList<LinearRespListModel> = ArrayList()


    private fun onSelectMaterial(item: LinearRespListModel, position: Int) {
        Log.d(TAG, "onAlertClick: $position")
        updateButtonState()
    }

    fun updateButtonState() {

        //val isMaterialSelected = linearAdapter.list.any { it.isSelected == true && (it.units ?: 0) > 0 }
        binding.headerInclude.btnSave.isEnabled = linearAdapter.selectedLinerID != -1
        binding.headerInclude.btnSave.alpha = if (linearAdapter.selectedLinerID != -1) 1f else 0.5f  // Adjust transparency
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        dialog = progressDialog(requireContext())
        binding.headerInclude.backButton.setOnClickListener {
            findNavController().popBackStack()
        }

        binding.headerInclude.btnSave.setOnClickListener {

            if (args.isEditable == 1) {
                updateLinearLineItem()
            } else {
                addLinearLineItem()
            }
        }

        binding.headerInclude.btnSave.isEnabled = false
        binding.headerInclude.btnSave.alpha = 0.5f

        binding.headerInclude.addPlaterTitle.text =  " New Line Item #" + args.lineItem

        binding.btnAddLineItem.setOnClickListener {

            if (args.lineEstimateType == "square_foot") {
                showAddSqaure()
            } else {
                showAddLineal()
            }
        }

        if (args.lineEstimateType == "square_foot") {
            binding.title.text = "Square Foot Costs"
        }


        binding.linealRecylerView.apply {
            setVerticalLayout()
            adapter = linearAdapter
        }

        if(args.isEditable == 1) {
            //binding.txtFullName.visibility = View.GONE
            //binding.edTxtFullName.visibility = View.GONE
            binding.headerInclude.addPlaterTitle.text =  "Update Line Item"

            binding.headerInclude.btnSave.isEnabled = true
            binding.headerInclude.btnSave.alpha = 1.0f
        }
    }

    private fun getLinearList() {

        baasViewModel.getSquareFootLinealFootCosts(args.lineEstimateType)
            .observe(viewLifecycleOwner) {
            when (it.status) {
                Status.ERROR -> {
                    snackBar(it.message ?: "Server Error")
                    dialog.dismiss()
                }
                Status.LOADING -> {
                    dialog.show()
                }

                Status.SUCCESS -> {
                    dialog.dismiss()

                    linearList.clear()
                    it.data?.list?.let { it1 ->
                        linearList.addAll(it1)

                        if (args.isEditable == 1) {

                            linearList.forEach { material ->
                                val matchingItem = args.materialItem.list?.find { newItem -> newItem.name == material.name }
                                if (matchingItem != null) {
                                    material.isSelected = true
                                    linearAdapter.selectedLinerID = material.id!!
                                    material.units = matchingItem.quantity // Set units from quantity
                                }
                            }
                        }
                    }


                    linearAdapter.refresh(linearList)
                }
            }
        }
    }

    private fun addNewLinearItem(cost: String, laborCost: String, name: String, sheet: BottomSheetDialog) {

        baasViewModel.addLinealFootCost(name, cost.toIntOrNull() ?: 0, laborCost.toIntOrNull() ?: 0, 0)
            .observe(viewLifecycleOwner) {
                when (it.status) {
                    Status.ERROR -> {
                        snackBar(it.message ?: "Server Error")
                        dialog.dismiss()
                    }
                    Status.LOADING -> {
                        dialog.show()
                    }

                    Status.SUCCESS -> {
                        dialog.dismiss()
                        sheet.dismiss()
                        getLinearList()
                    }
                }
            }
    }

    private fun addNewSquareItem(cost: String, laborCost: String, name: String, sheet: BottomSheetDialog) {

        baasViewModel.addSquareFootCost(name, cost.toIntOrNull() ?: 0, laborCost.toIntOrNull() ?: 0, 0)
            .observe(viewLifecycleOwner) {
                when (it.status) {
                    Status.ERROR -> {
                        snackBar(it.message ?: "Server Error")
                        dialog.dismiss()
                    }
                    Status.LOADING -> {
                        dialog.show()
                    }

                    Status.SUCCESS -> {
                        dialog.dismiss()
                        sheet.dismiss()
                        getLinearList()
                    }
                }
            }
    }

    private fun addLinearLineItem() {

        val selectedItem = linearAdapter.list.find { it.id == linearAdapter.selectedLinerID }

        if (selectedItem != null) {
            val entries: List<CreateLineEntriesReqModel> = listOf(
                CreateLineEntriesReqModel(
                    quantity = selectedItem?.units ?: 0, // Ensure selectedItem is not null
                    item_id = selectedItem?.id ?: 0
                )
            )

            baasViewModel.addLineItem(CreateLineItemReqModel(args.lineDescrition, args.lineEstimateType, args.projectID, 30, entries))
                .observe(viewLifecycleOwner) {
                    when (it.status) {
                        Status.ERROR -> {
                            snackBar(it.message ?: "Server Error")
                            dialog.dismiss()
                        }
                        Status.LOADING -> {
                            dialog.show()
                        }

                        Status.SUCCESS -> {
                            dialog.dismiss()
                            
                            // Update project status to draft
                            updateProjectStatusToDraft()

                            findNavController().popBackStack(R.id.lineItemView, false)

//                        findNavController().navigate(
//                            R.id.lineItemView,
//                            null,
//                            NavOptions.Builder()
//                                .setPopUpTo(R.id.addLineItemFragment, true) // Removes intermediate fragments
//                                .build()
//                        )
                        }
                    }
                }
        }




    }

    private fun updateLinearLineItem() {

        val selectedItem = linearAdapter.list.find { it.id == linearAdapter.selectedLinerID }
        if (selectedItem != null) {
            val entries: List<UpdateLineEntriesReqModel> = listOf(
                UpdateLineEntriesReqModel(
                    quantity = selectedItem.units ?: 0, // Safe null check
                    item_id = selectedItem.id ?: 0,
                    selectedItem.selectedID,
                    selectedItem.name.toString()
                )
            )

            baasViewModel.updateLineItem(args.itemLineID, UpdateLineItemReqModel(
                args.lineDescrition,
                args.lineEstimateType,
                args.projectID,
                args.labourHours.toInt(),
                entries
            )
            )
                .observe(viewLifecycleOwner) {
                    when (it.status) {
                        Status.ERROR -> {
                            snackBar("Server Error")
                            dialog.dismiss()
                        }
                        Status.LOADING -> {
                            dialog.show()
                        }

                        Status.SUCCESS -> {
                            dialog.dismiss()
                            
                            // Update project status to draft
                            updateProjectStatusToDraft()

                            findNavController().popBackStack(R.id.lineItemView, false)

//                        findNavController().navigate(
//                            R.id.lineItemView,
//                            null,
//                            NavOptions.Builder()
//                                .setPopUpTo(R.id.addLineItemFragment, true) // Removes intermediate fragments
//                                .build()
//                        )
                        }
                    }
                }
        }




    }

    private fun showAddLineal(){
        val sheet = BottomSheetDialog(requireContext(), R.style.BottomSheetDialogTheme)
        val view = BottomAddLinealBinding.inflate(layoutInflater)

        sheet.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_NOTHING)
        sheet.setOnShowListener { dialogInterface ->
            val bottomSheetDialog = dialogInterface as BottomSheetDialog
            val bottomSheet = bottomSheetDialog.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet)

            if (bottomSheet != null) {
                // Set height to full screen
                bottomSheet.layoutParams.height = ViewGroup.LayoutParams.MATCH_PARENT

                // Add a top margin of 100px
                val layoutParams = bottomSheet.layoutParams as ViewGroup.MarginLayoutParams
                layoutParams.topMargin = 0 // Adjust margin as needed
                layoutParams.bottomMargin = 0
                bottomSheet.layoutParams = layoutParams

                // Ensure the bottom sheet is expanded
                val behavior = BottomSheetBehavior.from(bottomSheet)
                behavior.state = BottomSheetBehavior.STATE_EXPANDED
                behavior.isFitToContents = false
                behavior.skipCollapsed = true
            }

        }

        view.apply {

            checkBox1.visibility = View.VISIBLE
            edTxtProfitOverHead.setText("")
            remaingCost.setText("0 Remaining")

            btnSave.isEnabled = false
            btnSave.alpha = 0.5f


            edTxtLinealName.doAfterTextChanged {

                var profitOver = it.toString().isNotEmpty()
                btnSave.isEnabled = profitOver
                btnSave.alpha = if (btnSave.isEnabled) 1f else 0.5f
            }

            edTxtLinealCost.doAfterTextChanged {

                var profitOver = it.toString().toIntOrNull() ?: 0
                val profitOverhead = if (pref.defaultProfitOverhead == 0) 1 else pref.defaultProfitOverhead

                profitOver = (profitOver * profitOverhead) / 100
                val remaining = (edTxtLinealCost.text.toString().toIntOrNull() ?: 0) -  profitOver


                remaingCost.setText("$remaining Remaining")


                Log.d("Ayodeji", "showAddLineal:  $profitOver  $it  ${pref.defaultProfitOverhead}  ${profitOverhead}  ")

                edTxtProfitOverHead.setText(""+profitOver)

                var laboutCost = edTxtLabourCost.text.toString().toIntOrNull() ?: 0
                val materialCost = remaining - laboutCost
                edTxtMaterialCost.setText(""+materialCost)

                val isLinealCostValid = it.toString().isNotEmpty() && try {
                    val value = edTxtLinealCost.text.toString().toFloat()
                    value > 0
                } catch (e: NumberFormatException) {
                    false
                }

                btnSave.isEnabled = isLinealCostValid && materialCost > 0
                btnSave.alpha = if (btnSave.isEnabled) 1f else 0.5f
                tvFullNameError.visibility = if(isLinealCostValid) View.GONE else View.VISIBLE
                tvMaterialCostError.visibility = if(materialCost >= 0) View.GONE else View.VISIBLE
            }

            edTxtLabourCost.doAfterTextChanged {

                var laboutCost = it.toString().toIntOrNull() ?: 0

                var profitOver = edTxtLinealCost.text.toString().toIntOrNull() ?: 0
                profitOver = (profitOver * pref.defaultProfitOverhead) / 100
                val remaining = (edTxtLinealCost.text.toString().toIntOrNull() ?: 0) -  profitOver
                val materialCost = remaining - laboutCost
                edTxtMaterialCost.setText(""+materialCost)

                val isLabourCostValid = edTxtLabourCost.text.isNotEmpty() && try {
                    val value = edTxtLabourCost.text.toString().toFloat()
                    value > 0
                } catch (e: NumberFormatException) {
                    false
                }

                btnSave.isEnabled = isLabourCostValid && materialCost > 0
                btnSave.alpha = if (btnSave.isEnabled) 1f else 0.5f

                tvMaterialCostError.visibility = if(materialCost >= 0) View.GONE else View.VISIBLE
            }

            backButton.setOnClickListener {
                sheet.dismiss()
            }
            btnSave.setOnClickListener {


                if(edTxtLinealName.text.toString().isEmpty()) {
                    Toast.makeText(context, "Please enter lineal name", Toast.LENGTH_SHORT).show();
                } else if(edTxtLinealCost.text.isEmpty()) {
                    Toast.makeText(context, "Please enter lineal foot cost", Toast.LENGTH_SHORT).show();
                } else if(edTxtLabourCost.text.isEmpty()) {
                    Toast.makeText(context, "Please enter labour foot cost", Toast.LENGTH_SHORT).show();
                } else {

                    var profitOver = edTxtLinealCost.text.toString().toIntOrNull() ?: 0

                    profitOver = (profitOver * pref.defaultProfitOverhead) / 100
                    //val remaining = (edTxtLinealCost.text.toString().toIntOrNull() ?: 0) -  profitOver
                    addNewLinearItem(edTxtLinealCost.text.toString(), edTxtLabourCost.text.toString(), edTxtLinealName.text.toString(), sheet)
                }
            }

            line1.setOnClickListener() {
                val imm = requireActivity().getSystemService(Activity.INPUT_METHOD_SERVICE) as InputMethodManager
                imm.hideSoftInputFromWindow(view.root.windowToken, 0)

            }

        }

        sheet.setOnDismissListener {
            requireActivity().hideSoftKeyboard()
        }
        sheet.setCancelable(true)
        sheet.setContentView(view.root)
        sheet.show()
    }

    private fun updateProjectStatusToDraft() {
        // Get project details to retrieve necessary data for updateProject
        baasViewModel.getSingleProjectDetails(args.projectID).observe(viewLifecycleOwner) { projectResource ->
            if (projectResource.status == Status.SUCCESS && projectResource.data?.error == false) {
                val clientDetails = projectResource.data?.client_details
                val totals = projectResource.data?.totals
                val changeCount = 0
                val customerId = clientDetails?.customer_id ?: 0
                val userId = clientDetails?.user_id ?: 0
                val profitOverhead = totals?.total_profit_overhead?.toString() ?: "0"
                val hourlyRate = totals?.labour_budget?.toString() ?: "0"

                baasViewModel.updateProject(
                    changeCount = changeCount,
                    customerId = customerId,
                    userId = userId,
                    status = 3, // draft
                    profitOverhead = profitOverhead,
                    hourlyRate = hourlyRate,
                    id = args.projectID
                ).observe(viewLifecycleOwner) { updateResource ->
                    when (updateResource.status) {
                        Status.LOADING -> {
                            // Show loading if needed
                        }
                        Status.SUCCESS -> {
                            if (updateResource.data?.error == false) {
                                Log.d("LinealLineItemFragment", "Project status updated to Draft (3)")
                            }
                        }
                        Status.ERROR -> {
                            Log.e("LinealLineItemFragment", "Failed to update project status: ${updateResource.message}")
                        }
                    }
                }
            }
        }
    }

    private fun showAddSqaure() {
        val sheet = BottomSheetDialog(requireContext(), R.style.BottomSheetDialogTheme)
        val view = BottomAddLinealBinding.inflate(layoutInflater)

        sheet.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_NOTHING)
        sheet.setOnShowListener { dialogInterface ->
            val bottomSheetDialog = dialogInterface as BottomSheetDialog
            val bottomSheet = bottomSheetDialog.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet)

            if (bottomSheet != null) {
                // Set height to full screen
                bottomSheet.layoutParams.height = ViewGroup.LayoutParams.MATCH_PARENT

                // Add a top margin of 100px
                val layoutParams = bottomSheet.layoutParams as ViewGroup.MarginLayoutParams
                layoutParams.topMargin = 0 // Adjust margin as needed
                layoutParams.bottomMargin = 0
                bottomSheet.layoutParams = layoutParams

                // Ensure the bottom sheet is expanded
                val behavior = BottomSheetBehavior.from(bottomSheet)
                behavior.state = BottomSheetBehavior.STATE_EXPANDED
                behavior.isFitToContents = false
                behavior.skipCollapsed = true
            }

        }

        view.apply {

            checkBox1.visibility = View.VISIBLE
            addHeading.text = "New Square Foot Cost"
            tvFullName.setText("Square foot cost")
            edTxtProfitOverHead.setText("")
            remaingCost.setText("0 Remaining")

            btnSave.isEnabled = false
            btnSave.alpha = 0.5f


            edTxtLinealName.doAfterTextChanged {

                var profitOver = it.toString().isNotEmpty()
                btnSave.isEnabled = profitOver
                btnSave.alpha = if (btnSave.isEnabled) 1f else 0.5f
            }

            edTxtLinealCost.doAfterTextChanged {

                var profitOver = it.toString().toIntOrNull() ?: 0
                val profitOverhead = if (pref.defaultProfitOverhead == 0) 1 else pref.defaultProfitOverhead

                profitOver = (profitOver * profitOverhead) / 100
                val remaining = (edTxtLinealCost.text.toString().toIntOrNull() ?: 0) -  profitOver


                remaingCost.setText("$remaining Remaining")
                edTxtProfitOverHead.setText(""+profitOver)

                var laboutCost = edTxtLabourCost.text.toString().toIntOrNull() ?: 0
                val materialCost = remaining - laboutCost
                edTxtMaterialCost.setText(""+materialCost)

                val isLinealCostValid = it.toString().isNotEmpty() && try {
                    val value = edTxtLinealCost.text.toString().toFloat()
                    value > 0
                } catch (e: NumberFormatException) {
                    false
                }

                btnSave.isEnabled = isLinealCostValid && materialCost > 0
                btnSave.alpha = if (btnSave.isEnabled) 1f else 0.5f
                tvFullNameError.visibility = if(isLinealCostValid) View.GONE else View.VISIBLE
                tvFullNameError.text = "Square foot cost cannot be zero."
                tvMaterialCostError.visibility = if(materialCost >= 0) View.GONE else View.VISIBLE
            }

            edTxtLabourCost.doAfterTextChanged {

                var laboutCost = it.toString().toIntOrNull() ?: 0

                var profitOver = edTxtLinealCost.text.toString().toIntOrNull() ?: 0
                profitOver = (profitOver * pref.defaultProfitOverhead) / 100
                val remaining = (edTxtLinealCost.text.toString().toIntOrNull() ?: 0) -  profitOver
                val materialCost = remaining - laboutCost
                edTxtMaterialCost.setText(""+materialCost)

                val isLabourCostValid = edTxtLabourCost.text.isNotEmpty() && try {
                    val value = edTxtLabourCost.text.toString().toFloat()
                    value > 0
                } catch (e: NumberFormatException) {
                    false
                }

                btnSave.isEnabled = isLabourCostValid && materialCost > 0
                btnSave.alpha = if (btnSave.isEnabled) 1f else 0.5f

                tvMaterialCostError.visibility = if(materialCost >= 0) View.GONE else View.VISIBLE
            }

            backButton.setOnClickListener {
                sheet.dismiss()
            }
            btnSave.setOnClickListener {


                if(edTxtLinealName.text.toString().isEmpty()) {
                    Toast.makeText(context, "Please enter lineal name", Toast.LENGTH_SHORT).show();
                } else if(edTxtLinealCost.text.isEmpty()) {
                    Toast.makeText(context, "Please enter lineal foot cost", Toast.LENGTH_SHORT).show();
                } else if(edTxtLabourCost.text.isEmpty()) {
                    Toast.makeText(context, "Please enter labour foot cost", Toast.LENGTH_SHORT).show();
                } else {

                    var profitOver = edTxtLinealCost.text.toString().toIntOrNull() ?: 0

                    profitOver = (profitOver * pref.defaultProfitOverhead) / 100
                    val remaining = (edTxtLinealCost.text.toString().toIntOrNull() ?: 0) -  profitOver
                    addNewSquareItem(edTxtLinealCost.text.toString(), edTxtLabourCost.text.toString(), edTxtLinealName.text.toString(), sheet)
                }
            }


            line1.setOnClickListener() {
                val imm = requireActivity().getSystemService(Activity.INPUT_METHOD_SERVICE) as InputMethodManager
                imm.hideSoftInputFromWindow(view.root.windowToken, 0)

            }
        }

        sheet.setOnDismissListener {
            requireActivity().hideSoftKeyboard()
        }

        sheet.setCancelable(true)
        sheet.setContentView(view.root)
        sheet.show()
    }

    override fun onPause() {
        super.onPause()
        binding.headerInclude.btnSave.invisible()
    }
  override fun onResume() {
            super.onResume()
            (activity as AppCompatActivity?)?.supportActionBar?.hide()
      binding.headerInclude.backButton.show()
      binding.headerInclude.btnSave.show()
      getLinearList()
        }

        override fun onStop() {
            super.onStop()
            (activity as AppCompatActivity?)?.supportActionBar?.show()
        }

}
    
