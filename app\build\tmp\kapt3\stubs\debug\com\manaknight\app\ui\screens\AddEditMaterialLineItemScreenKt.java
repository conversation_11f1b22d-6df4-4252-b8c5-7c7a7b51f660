package com.manaknight.app.ui.screens;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000X\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0014\u001a\u00c5\u0001\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u00032\u0006\u0010\u0007\u001a\u00020\u00032\u0006\u0010\b\u001a\u00020\u00052\u0006\u0010\t\u001a\u00020\u00052\b\u0010\n\u001a\u0004\u0018\u00010\u00052\b\u0010\u000b\u001a\u0004\u0018\u00010\f2\b\u0010\r\u001a\u0004\u0018\u00010\u00032\u0006\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\u00032\f\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u00050\u00142\u0012\u0010\u0015\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00170\u00160\u00142\u0006\u0010\u0018\u001a\u00020\u00192\u0012\u0010\u001a\u001a\u000e\u0012\u0004\u0012\u00020\u0019\u0012\u0004\u0012\u00020\u00010\u001b2\b\b\u0002\u0010\u001c\u001a\u00020\u00192\b\b\u0002\u0010\u001d\u001a\u00020\u0019H\u0003\u00a2\u0006\u0002\u0010\u001e\u001as\u0010\u001f\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u00032\u0006\u0010\u0007\u001a\u00020\u00032\u0006\u0010\b\u001a\u00020\u00052\u0006\u0010\t\u001a\u00020\u00052\b\u0010\n\u001a\u0004\u0018\u00010\u00052\b\u0010\u000b\u001a\u0004\u0018\u00010\f2\b\u0010\r\u001a\u0004\u0018\u00010\u00032\u0006\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\u0003H\u0007\u00a2\u0006\u0002\u0010 \u001a \u0010!\u001a\u00020\u00012\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u00032\u0006\u0010\u000e\u001a\u00020\u000fH\u0003\u001a@\u0010\"\u001a\u00020\u00012\f\u0010#\u001a\b\u0012\u0004\u0012\u00020\u00010$2\u0018\u0010%\u001a\u0014\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010&2\u0006\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\u0003H\u0007\u001a\\\u0010\'\u001a\u00020\u00012\u0006\u0010(\u001a\u00020\u00052\u0012\u0010)\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010\u001b2\u0006\u0010*\u001a\u00020\u00052\u0012\u0010+\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010\u001b2\u0006\u0010,\u001a\u00020\u00192\u0012\u0010-\u001a\u000e\u0012\u0004\u0012\u00020\u0019\u0012\u0004\u0012\u00020\u00010\u001bH\u0007\u001aP\u0010.\u001a\u00020\u00012\u0006\u0010(\u001a\u00020\u00052\u0006\u0010*\u001a\u00020\u00052\f\u0010/\u001a\b\u0012\u0004\u0012\u00020\u00010$2\u0018\u00100\u001a\u0014\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010&2\u0006\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\u0003H\u0007\u001a8\u00101\u001a\u00020\u00012\u0006\u00102\u001a\u00020\u00172\u0012\u00103\u001a\u000e\u0012\u0004\u0012\u00020\u0019\u0012\u0004\u0012\u00020\u00010\u001b2\u0012\u00104\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010\u001bH\u0007\u001a[\u00105\u001a\u00020\u00012\u0006\u0010\u0006\u001a\u00020\u00032\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\b\u001a\u00020\u00052\u0006\u0010\t\u001a\u00020\u00052\u0006\u00106\u001a\u00020\u00032\f\u00107\u001a\b\u0012\u0004\u0012\u00020\u00170\u00162\b\u0010\r\u001a\u0004\u0018\u00010\u00032\u0006\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u000e\u001a\u00020\u000f\u00a2\u0006\u0002\u00108\u001a\u0018\u00109\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0010\u001a\u00020\u0011H\u0002\u00a8\u0006:"}, d2 = {"AddEditMaterialLineItemDetailContent", "", "projectID", "", "customerName", "", "isEditable", "lineItemNumber", "lineDescription", "lineEstimateType", "labourHours", "materialItem", "Lcom/manaknight/app/model/remote/profitPro/MaterialRespListModel2;", "itemLineID", "navController", "Landroidx/navigation/NavController;", "baasViewModel", "Lcom/manaknight/app/viewmodels/BaasViewModel;", "userId", "laborHoursState", "Landroidx/compose/runtime/MutableState;", "materialListState", "", "Lcom/manaknight/app/model/remote/profitPro/MaterialRespListModel;", "showAddNewMaterialDialog", "", "onShowAddNewMaterialDialogChange", "Lkotlin/Function1;", "showTopBar", "showDetailHeader", "(ILjava/lang/String;IILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Lcom/manaknight/app/model/remote/profitPro/MaterialRespListModel2;Ljava/lang/Integer;Landroidx/navigation/NavController;Lcom/manaknight/app/viewmodels/BaasViewModel;ILandroidx/compose/runtime/MutableState;Landroidx/compose/runtime/MutableState;ZLkotlin/jvm/functions/Function1;ZZ)V", "AddEditMaterialLineItemScreen", "(ILjava/lang/String;IILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Lcom/manaknight/app/model/remote/profitPro/MaterialRespListModel2;Ljava/lang/Integer;Landroidx/navigation/NavController;Lcom/manaknight/app/viewmodels/BaasViewModel;I)V", "AddEditMaterialLineItemTopBar", "AddNewMaterialDialog", "onDismissRequest", "Lkotlin/Function0;", "onMaterialAdded", "Lkotlin/Function2;", "AddNewMaterialDialogContentBody", "materialName", "onMaterialNameChange", "unitCost", "onUnitCostChange", "shouldAddToDefaultList", "onShouldAddToDefaultListChange", "AddNewMaterialDialogHeader", "onDismiss", "onSave", "MaterialListItem", "material", "onItemSelected", "onUnitsChanged", "saveMaterialLineItem", "laborHours", "selectedMaterials", "(IILjava/lang/String;Ljava/lang/String;ILjava/util/List;Ljava/lang/Integer;Lcom/manaknight/app/viewmodels/BaasViewModel;Landroidx/navigation/NavController;)V", "updateProjectStatusToDraft", "app_debug"})
public final class AddEditMaterialLineItemScreenKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void AddEditMaterialLineItemScreen(int projectID, @org.jetbrains.annotations.NotNull()
    java.lang.String customerName, int isEditable, int lineItemNumber, @org.jetbrains.annotations.NotNull()
    java.lang.String lineDescription, @org.jetbrains.annotations.NotNull()
    java.lang.String lineEstimateType, @org.jetbrains.annotations.Nullable()
    java.lang.String labourHours, @org.jetbrains.annotations.Nullable()
    com.manaknight.app.model.remote.profitPro.MaterialRespListModel2 materialItem, @org.jetbrains.annotations.Nullable()
    java.lang.Integer itemLineID, @org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController navController, @org.jetbrains.annotations.NotNull()
    com.manaknight.app.viewmodels.BaasViewModel baasViewModel, int userId) {
    }
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    private static final void AddEditMaterialLineItemDetailContent(int projectID, java.lang.String customerName, int isEditable, int lineItemNumber, java.lang.String lineDescription, java.lang.String lineEstimateType, java.lang.String labourHours, com.manaknight.app.model.remote.profitPro.MaterialRespListModel2 materialItem, java.lang.Integer itemLineID, androidx.navigation.NavController navController, com.manaknight.app.viewmodels.BaasViewModel baasViewModel, int userId, androidx.compose.runtime.MutableState<java.lang.String> laborHoursState, androidx.compose.runtime.MutableState<java.util.List<com.manaknight.app.model.remote.profitPro.MaterialRespListModel>> materialListState, boolean showAddNewMaterialDialog, kotlin.jvm.functions.Function1<? super java.lang.Boolean, kotlin.Unit> onShowAddNewMaterialDialogChange, boolean showTopBar, boolean showDetailHeader) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void MaterialListItem(@org.jetbrains.annotations.NotNull()
    com.manaknight.app.model.remote.profitPro.MaterialRespListModel material, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Boolean, kotlin.Unit> onItemSelected, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onUnitsChanged) {
    }
    
    public static final void saveMaterialLineItem(int isEditable, int projectID, @org.jetbrains.annotations.NotNull()
    java.lang.String lineDescription, @org.jetbrains.annotations.NotNull()
    java.lang.String lineEstimateType, int laborHours, @org.jetbrains.annotations.NotNull()
    java.util.List<com.manaknight.app.model.remote.profitPro.MaterialRespListModel> selectedMaterials, @org.jetbrains.annotations.Nullable()
    java.lang.Integer itemLineID, @org.jetbrains.annotations.NotNull()
    com.manaknight.app.viewmodels.BaasViewModel baasViewModel, @org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController navController) {
    }
    
    private static final void updateProjectStatusToDraft(int projectID, com.manaknight.app.viewmodels.BaasViewModel baasViewModel) {
    }
    
    @android.annotation.SuppressLint(value = {"SuspiciousIndentation"})
    @androidx.compose.runtime.Composable()
    public static final void AddNewMaterialDialog(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismissRequest, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.lang.String, ? super java.lang.String, kotlin.Unit> onMaterialAdded, @org.jetbrains.annotations.NotNull()
    com.manaknight.app.viewmodels.BaasViewModel baasViewModel, int userId) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void AddNewMaterialDialogHeader(@org.jetbrains.annotations.NotNull()
    java.lang.String materialName, @org.jetbrains.annotations.NotNull()
    java.lang.String unitCost, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.lang.String, ? super java.lang.String, kotlin.Unit> onSave, @org.jetbrains.annotations.NotNull()
    com.manaknight.app.viewmodels.BaasViewModel baasViewModel, int userId) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void AddNewMaterialDialogContentBody(@org.jetbrains.annotations.NotNull()
    java.lang.String materialName, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onMaterialNameChange, @org.jetbrains.annotations.NotNull()
    java.lang.String unitCost, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onUnitCostChange, boolean shouldAddToDefaultList, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Boolean, kotlin.Unit> onShouldAddToDefaultListChange) {
    }
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    private static final void AddEditMaterialLineItemTopBar(java.lang.String customerName, int isEditable, androidx.navigation.NavController navController) {
    }
}