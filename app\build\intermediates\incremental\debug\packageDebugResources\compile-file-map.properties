#Fri Aug 01 19:42:19 WAT 2025
Manaknight.app-main-7\:/anim/scanner_animation.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\scanner_animation.xml
Manaknight.app-main-7\:/color/bottom_nav_color.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\color\\bottom_nav_color.xml
Manaknight.app-main-7\:/drawable/add_shape.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\add_shape.xml
Manaknight.app-main-7\:/drawable/apple.webp=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\apple.webp
Manaknight.app-main-7\:/drawable/bg_round_corners.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\bg_round_corners.xml
Manaknight.app-main-7\:/drawable/bg_round_corners_2.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\bg_round_corners_2.xml
Manaknight.app-main-7\:/drawable/bg_round_white.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\bg_round_white.xml
Manaknight.app-main-7\:/drawable/bg_stat_card.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\bg_stat_card.xml
Manaknight.app-main-7\:/drawable/bottom_sheet_background.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\bottom_sheet_background.xml
Manaknight.app-main-7\:/drawable/bottom_sheet_rounded_background.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\bottom_sheet_rounded_background.xml
Manaknight.app-main-7\:/drawable/chat_input_shape.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\chat_input_shape.xml
Manaknight.app-main-7\:/drawable/check.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\check.png
Manaknight.app-main-7\:/drawable/checkbox_checked.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\checkbox_checked.xml
Manaknight.app-main-7\:/drawable/checkbox_color_selector.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\checkbox_color_selector.xml
Manaknight.app-main-7\:/drawable/checkbox_unchecked.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\checkbox_unchecked.xml
Manaknight.app-main-7\:/drawable/checked.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\checked.png
Manaknight.app-main-7\:/drawable/chevron_bottom.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\chevron_bottom.png
Manaknight.app-main-7\:/drawable/chevron_up.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\chevron_up.png
Manaknight.app-main-7\:/drawable/compney_health.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\compney_health.png
Manaknight.app-main-7\:/drawable/cross.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\cross.png
Manaknight.app-main-7\:/drawable/custom_checkbox.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\custom_checkbox.xml
Manaknight.app-main-7\:/drawable/custom_checkbox_selector.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\custom_checkbox_selector.xml
Manaknight.app-main-7\:/drawable/delete.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\delete.png
Manaknight.app-main-7\:/drawable/dialog_background.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\dialog_background.xml
Manaknight.app-main-7\:/drawable/edit.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\edit.png
Manaknight.app-main-7\:/drawable/filter_button_background.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\filter_button_background.xml
Manaknight.app-main-7\:/drawable/google.webp=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\google.webp
Manaknight.app-main-7\:/drawable/green_check.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\green_check.png
Manaknight.app-main-7\:/drawable/green_check_mark.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\green_check_mark.xml
Manaknight.app-main-7\:/drawable/ic_add.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_add.png
Manaknight.app-main-7\:/drawable/ic_arrow_right.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_arrow_right.png
Manaknight.app-main-7\:/drawable/ic_arrow_up_right.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_arrow_up_right.png
Manaknight.app-main-7\:/drawable/ic_baseline_add_24.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_baseline_add_24.xml
Manaknight.app-main-7\:/drawable/ic_baseline_camera_alt_24.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_baseline_camera_alt_24.xml
Manaknight.app-main-7\:/drawable/ic_baseline_close_24.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_baseline_close_24.xml
Manaknight.app-main-7\:/drawable/ic_baseline_image_24.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_baseline_image_24.xml
Manaknight.app-main-7\:/drawable/ic_baseline_play_circle_24.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_baseline_play_circle_24.xml
Manaknight.app-main-7\:/drawable/ic_baseline_send_24.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_baseline_send_24.xml
Manaknight.app-main-7\:/drawable/ic_baseline_video_library_24.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_baseline_video_library_24.xml
Manaknight.app-main-7\:/drawable/ic_cart.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_cart.xml
Manaknight.app-main-7\:/drawable/ic_close.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_close.xml
Manaknight.app-main-7\:/drawable/ic_default.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_default.xml
Manaknight.app-main-7\:/drawable/ic_delete_icon.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_delete_icon.png
Manaknight.app-main-7\:/drawable/ic_dropdown.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_dropdown.png
Manaknight.app-main-7\:/drawable/ic_edit_icon.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_edit_icon.png
Manaknight.app-main-7\:/drawable/ic_info_custom_fill.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_info_custom_fill.png
Manaknight.app-main-7\:/drawable/ic_launcher_background.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_launcher_background.xml
Manaknight.app-main-7\:/drawable/ic_loc_active.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_loc_active.png
Manaknight.app-main-7\:/drawable/ic_menu.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_menu.xml
Manaknight.app-main-7\:/drawable/ic_option_light.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_option_light.png
Manaknight.app-main-7\:/drawable/ic_options_bold.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_options_bold.png
Manaknight.app-main-7\:/drawable/ic_profile.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_profile.xml
Manaknight.app-main-7\:/drawable/ic_search.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_search.xml
Manaknight.app-main-7\:/drawable/icon_left.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\icon_left.png
Manaknight.app-main-7\:/drawable/image_shape.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\image_shape.xml
Manaknight.app-main-7\:/drawable/info.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\info.png
Manaknight.app-main-7\:/drawable/logout_icon.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\logout_icon.png
Manaknight.app-main-7\:/drawable/new_checked.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\new_checked.png
Manaknight.app-main-7\:/drawable/new_unchecked.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\new_unchecked.png
Manaknight.app-main-7\:/drawable/plus_button.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\plus_button.png
Manaknight.app-main-7\:/drawable/profit.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\profit.png
Manaknight.app-main-7\:/drawable/profit_overhead.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\profit_overhead.png
Manaknight.app-main-7\:/drawable/progress_bar.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\progress_bar.xml
Manaknight.app-main-7\:/drawable/receive_message_shape.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\receive_message_shape.xml
Manaknight.app-main-7\:/drawable/rounded_edittext.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\rounded_edittext.xml
Manaknight.app-main-7\:/drawable/rounded_edittext2.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\rounded_edittext2.xml
Manaknight.app-main-7\:/drawable/rounded_edittext2_none_editable.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\rounded_edittext2_none_editable.xml
Manaknight.app-main-7\:/drawable/rounded_edittext_none_editable.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\rounded_edittext_none_editable.xml
Manaknight.app-main-7\:/drawable/rounded_textview.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\rounded_textview.xml
Manaknight.app-main-7\:/drawable/rounded_textview_bg.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\rounded_textview_bg.xml
Manaknight.app-main-7\:/drawable/select_camera_shape.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\select_camera_shape.xml
Manaknight.app-main-7\:/drawable/select_image_shape.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\select_image_shape.xml
Manaknight.app-main-7\:/drawable/select_video_shape.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\select_video_shape.xml
Manaknight.app-main-7\:/drawable/send_button_shape.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\send_button_shape.xml
Manaknight.app-main-7\:/drawable/send_message_shape.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\send_message_shape.xml
Manaknight.app-main-7\:/drawable/setting_icon.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\setting_icon.png
Manaknight.app-main-7\:/drawable/subscription_icon.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\subscription_icon.png
Manaknight.app-main-7\:/drawable/tab_account.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\tab_account.xml
Manaknight.app-main-7\:/drawable/tab_account_selected.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\tab_account_selected.png
Manaknight.app-main-7\:/drawable/tab_account_unselected.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\tab_account_unselected.png
Manaknight.app-main-7\:/drawable/tab_cost.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\tab_cost.xml
Manaknight.app-main-7\:/drawable/tab_cost_selected.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\tab_cost_selected.png
Manaknight.app-main-7\:/drawable/tab_cost_unselected.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\tab_cost_unselected.png
Manaknight.app-main-7\:/drawable/tab_home.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\tab_home.xml
Manaknight.app-main-7\:/drawable/tab_home_selected.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\tab_home_selected.png
Manaknight.app-main-7\:/drawable/tab_home_unselected.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\tab_home_unselected.png
Manaknight.app-main-7\:/drawable/tab_project.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\tab_project.xml
Manaknight.app-main-7\:/drawable/tab_project_selected.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\tab_project_selected.png
Manaknight.app-main-7\:/drawable/tab_project_unselected.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\tab_project_unselected.png
Manaknight.app-main-7\:/drawable/tab_team.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\tab_team.xml
Manaknight.app-main-7\:/drawable/tab_team_selected.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\tab_team_selected.png
Manaknight.app-main-7\:/drawable/tab_team_unselected.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\tab_team_unselected.png
Manaknight.app-main-7\:/drawable/unchecked.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\unchecked.png
Manaknight.app-main-7\:/drawable/user_icon.png=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\user_icon.png
Manaknight.app-main-7\:/font/inter.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\font\\inter.xml
Manaknight.app-main-7\:/font/inter_18pt_bold.ttf=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\font\\inter_18pt_bold.ttf
Manaknight.app-main-7\:/font/inter_18pt_medium.ttf=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\font\\inter_18pt_medium.ttf
Manaknight.app-main-7\:/font/inter_18pt_regular.ttf=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\font\\inter_18pt_regular.ttf
Manaknight.app-main-7\:/font/inter_18pt_semibold.ttf=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\font\\inter_18pt_semibold.ttf
Manaknight.app-main-7\:/font/medium.ttf=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\font\\medium.ttf
Manaknight.app-main-7\:/font/semibold.ttf=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\font\\semibold.ttf
Manaknight.app-main-7\:/font/sf_pro.ttf=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\font\\sf_pro.ttf
Manaknight.app-main-7\:/font/sf_pro_italic.ttf=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\font\\sf_pro_italic.ttf
Manaknight.app-main-7\:/font/sfpro.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\font\\sfpro.xml
Manaknight.app-main-7\:/menu/bottom_nav_menu.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\menu\\bottom_nav_menu.xml
Manaknight.app-main-7\:/mipmap-anydpi-v26/ic_launcher.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-anydpi-v26\\ic_launcher.xml
Manaknight.app-main-7\:/mipmap-anydpi-v26/ic_launcher_round.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-anydpi-v26\\ic_launcher_round.xml
Manaknight.app-main-7\:/mipmap-hdpi/ic_launcher.webp=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-hdpi-v4\\ic_launcher.webp
Manaknight.app-main-7\:/mipmap-hdpi/ic_launcher_foreground.webp=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-hdpi-v4\\ic_launcher_foreground.webp
Manaknight.app-main-7\:/mipmap-hdpi/ic_launcher_round.webp=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-hdpi-v4\\ic_launcher_round.webp
Manaknight.app-main-7\:/mipmap-mdpi/ic_launcher.webp=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-mdpi-v4\\ic_launcher.webp
Manaknight.app-main-7\:/mipmap-mdpi/ic_launcher_foreground.webp=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-mdpi-v4\\ic_launcher_foreground.webp
Manaknight.app-main-7\:/mipmap-mdpi/ic_launcher_round.webp=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-mdpi-v4\\ic_launcher_round.webp
Manaknight.app-main-7\:/mipmap-xhdpi/ic_launcher.webp=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xhdpi-v4\\ic_launcher.webp
Manaknight.app-main-7\:/mipmap-xhdpi/ic_launcher_foreground.webp=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xhdpi-v4\\ic_launcher_foreground.webp
Manaknight.app-main-7\:/mipmap-xhdpi/ic_launcher_round.webp=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xhdpi-v4\\ic_launcher_round.webp
Manaknight.app-main-7\:/mipmap-xxhdpi/ic_launcher.webp=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxhdpi-v4\\ic_launcher.webp
Manaknight.app-main-7\:/mipmap-xxhdpi/ic_launcher_foreground.webp=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxhdpi-v4\\ic_launcher_foreground.webp
Manaknight.app-main-7\:/mipmap-xxhdpi/ic_launcher_round.webp=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxhdpi-v4\\ic_launcher_round.webp
Manaknight.app-main-7\:/mipmap-xxxhdpi/ic_launcher.webp=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxxhdpi-v4\\ic_launcher.webp
Manaknight.app-main-7\:/mipmap-xxxhdpi/ic_launcher_foreground.webp=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxxhdpi-v4\\ic_launcher_foreground.webp
Manaknight.app-main-7\:/mipmap-xxxhdpi/ic_launcher_round.webp=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxxhdpi-v4\\ic_launcher_round.webp
Manaknight.app-main-7\:/navigation/mobile_navigation.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\navigation\\mobile_navigation.xml
Manaknight.app-main-7\:/raw/loader.json=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\raw\\loader.json
Manaknight.app-main-7\:/xml/backup_rules.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\xml\\backup_rules.xml
Manaknight.app-main-7\:/xml/data_extraction_rules.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\xml\\data_extraction_rules.xml
Manaknight.app-main-7\:/xml/file_provider.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\xml\\file_provider.xml
Manaknight.app-packageDebugResources-4\:/layout-sw600dp-v13/activity_main.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout-sw600dp-v13\\activity_main.xml
Manaknight.app-packageDebugResources-4\:/layout-sw600dp-v13/bottom_add_draw.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout-sw600dp-v13\\bottom_add_draw.xml
Manaknight.app-packageDebugResources-4\:/layout-sw600dp-v13/bottom_add_employee.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout-sw600dp-v13\\bottom_add_employee.xml
Manaknight.app-packageDebugResources-4\:/layout-sw600dp-v13/bottom_add_lineal.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout-sw600dp-v13\\bottom_add_lineal.xml
Manaknight.app-packageDebugResources-4\:/layout-sw600dp-v13/bottom_add_material.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout-sw600dp-v13\\bottom_add_material.xml
Manaknight.app-packageDebugResources-4\:/layout-sw600dp-v13/bottom_edit_profile.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout-sw600dp-v13\\bottom_edit_profile.xml
Manaknight.app-packageDebugResources-4\:/layout-sw600dp-v13/bottom_select_customer.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout-sw600dp-v13\\bottom_select_customer.xml
Manaknight.app-packageDebugResources-4\:/layout-sw600dp-v13/bottom_sheet_month_filter.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout-sw600dp-v13\\bottom_sheet_month_filter.xml
Manaknight.app-packageDebugResources-4\:/layout-sw600dp-v13/bottom_sheet_multi_select_status_filter.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout-sw600dp-v13\\bottom_sheet_multi_select_status_filter.xml
Manaknight.app-packageDebugResources-4\:/layout-sw600dp-v13/bottom_sheet_status_filter.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout-sw600dp-v13\\bottom_sheet_status_filter.xml
Manaknight.app-packageDebugResources-4\:/layout-sw600dp-v13/bottom_update_password.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout-sw600dp-v13\\bottom_update_password.xml
Manaknight.app-packageDebugResources-4\:/layout-sw600dp-v13/bottom_update_profile.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout-sw600dp-v13\\bottom_update_profile.xml
Manaknight.app-packageDebugResources-4\:/layout-sw600dp-v13/dialog_add_lineal.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout-sw600dp-v13\\dialog_add_lineal.xml
Manaknight.app-packageDebugResources-4\:/layout-sw600dp-v13/dialog_add_material.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout-sw600dp-v13\\dialog_add_material.xml
Manaknight.app-packageDebugResources-4\:/layout-sw600dp-v13/dialog_add_square.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout-sw600dp-v13\\dialog_add_square.xml
Manaknight.app-packageDebugResources-4\:/layout-sw600dp-v13/dialog_forgetpassword.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout-sw600dp-v13\\dialog_forgetpassword.xml
Manaknight.app-packageDebugResources-4\:/layout-sw600dp-v13/dialog_resetpassword.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout-sw600dp-v13\\dialog_resetpassword.xml
Manaknight.app-packageDebugResources-4\:/layout-sw600dp-v13/fragment_accountview.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout-sw600dp-v13\\fragment_accountview.xml
Manaknight.app-packageDebugResources-4\:/layout-sw600dp-v13/fragment_add_line_items.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout-sw600dp-v13\\fragment_add_line_items.xml
Manaknight.app-packageDebugResources-4\:/layout-sw600dp-v13/fragment_companysetup.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout-sw600dp-v13\\fragment_companysetup.xml
Manaknight.app-packageDebugResources-4\:/layout-sw600dp-v13/fragment_completesetup.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout-sw600dp-v13\\fragment_completesetup.xml
Manaknight.app-packageDebugResources-4\:/layout-sw600dp-v13/fragment_create_customer.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout-sw600dp-v13\\fragment_create_customer.xml
Manaknight.app-packageDebugResources-4\:/layout-sw600dp-v13/fragment_create_estimation.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout-sw600dp-v13\\fragment_create_estimation.xml
Manaknight.app-packageDebugResources-4\:/layout-sw600dp-v13/fragment_dashboardview.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout-sw600dp-v13\\fragment_dashboardview.xml
Manaknight.app-packageDebugResources-4\:/layout-sw600dp-v13/fragment_draws.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout-sw600dp-v13\\fragment_draws.xml
Manaknight.app-packageDebugResources-4\:/layout-sw600dp-v13/fragment_forget_password.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout-sw600dp-v13\\fragment_forget_password.xml
Manaknight.app-packageDebugResources-4\:/layout-sw600dp-v13/fragment_home.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout-sw600dp-v13\\fragment_home.xml
Manaknight.app-packageDebugResources-4\:/layout-sw600dp-v13/fragment_line_items.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout-sw600dp-v13\\fragment_line_items.xml
Manaknight.app-packageDebugResources-4\:/layout-sw600dp-v13/fragment_linealsetup.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout-sw600dp-v13\\fragment_linealsetup.xml
Manaknight.app-packageDebugResources-4\:/layout-sw600dp-v13/fragment_linear_line_item.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout-sw600dp-v13\\fragment_linear_line_item.xml
Manaknight.app-packageDebugResources-4\:/layout-sw600dp-v13/fragment_login.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout-sw600dp-v13\\fragment_login.xml
Manaknight.app-packageDebugResources-4\:/layout-sw600dp-v13/fragment_material_line_item.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout-sw600dp-v13\\fragment_material_line_item.xml
Manaknight.app-packageDebugResources-4\:/layout-sw600dp-v13/fragment_materialsetup.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout-sw600dp-v13\\fragment_materialsetup.xml
Manaknight.app-packageDebugResources-4\:/layout-sw600dp-v13/fragment_profileview.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout-sw600dp-v13\\fragment_profileview.xml
Manaknight.app-packageDebugResources-4\:/layout-sw600dp-v13/fragment_reset_password.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout-sw600dp-v13\\fragment_reset_password.xml
Manaknight.app-packageDebugResources-4\:/layout-sw600dp-v13/fragment_sign_up.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout-sw600dp-v13\\fragment_sign_up.xml
Manaknight.app-packageDebugResources-4\:/layout-sw600dp-v13/fragment_squresetup.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout-sw600dp-v13\\fragment_squresetup.xml
Manaknight.app-packageDebugResources-4\:/layout-sw600dp-v13/fragment_subscription.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout-sw600dp-v13\\fragment_subscription.xml
Manaknight.app-packageDebugResources-4\:/layout-sw600dp-v13/header.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout-sw600dp-v13\\header.xml
Manaknight.app-packageDebugResources-4\:/layout-sw600dp-v13/item_customer.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout-sw600dp-v13\\item_customer.xml
Manaknight.app-packageDebugResources-4\:/layout-sw600dp-v13/item_draw.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout-sw600dp-v13\\item_draw.xml
Manaknight.app-packageDebugResources-4\:/layout-sw600dp-v13/item_employee.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout-sw600dp-v13\\item_employee.xml
Manaknight.app-packageDebugResources-4\:/layout-sw600dp-v13/item_line.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout-sw600dp-v13\\item_line.xml
Manaknight.app-packageDebugResources-4\:/layout-sw600dp-v13/item_line_lineal.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout-sw600dp-v13\\item_line_lineal.xml
Manaknight.app-packageDebugResources-4\:/layout-sw600dp-v13/item_line_material.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout-sw600dp-v13\\item_line_material.xml
Manaknight.app-packageDebugResources-4\:/layout-sw600dp-v13/item_line_total.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout-sw600dp-v13\\item_line_total.xml
Manaknight.app-packageDebugResources-4\:/layout-sw600dp-v13/item_lineal.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout-sw600dp-v13\\item_lineal.xml
Manaknight.app-packageDebugResources-4\:/layout-sw600dp-v13/item_material.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout-sw600dp-v13\\item_material.xml
Manaknight.app-packageDebugResources-4\:/layout/activity_main.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_main.xml
Manaknight.app-packageDebugResources-4\:/layout/bottom_add_draw.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\bottom_add_draw.xml
Manaknight.app-packageDebugResources-4\:/layout/bottom_add_employee.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\bottom_add_employee.xml
Manaknight.app-packageDebugResources-4\:/layout/bottom_add_lineal.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\bottom_add_lineal.xml
Manaknight.app-packageDebugResources-4\:/layout/bottom_add_material.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\bottom_add_material.xml
Manaknight.app-packageDebugResources-4\:/layout/bottom_edit_profile.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\bottom_edit_profile.xml
Manaknight.app-packageDebugResources-4\:/layout/bottom_select_customer.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\bottom_select_customer.xml
Manaknight.app-packageDebugResources-4\:/layout/bottom_sheet_month_filter.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\bottom_sheet_month_filter.xml
Manaknight.app-packageDebugResources-4\:/layout/bottom_sheet_multi_select_status_filter.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\bottom_sheet_multi_select_status_filter.xml
Manaknight.app-packageDebugResources-4\:/layout/bottom_sheet_status_filter.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\bottom_sheet_status_filter.xml
Manaknight.app-packageDebugResources-4\:/layout/bottom_update_password.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\bottom_update_password.xml
Manaknight.app-packageDebugResources-4\:/layout/bottom_update_profile.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\bottom_update_profile.xml
Manaknight.app-packageDebugResources-4\:/layout/dialog_add_lineal.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_add_lineal.xml
Manaknight.app-packageDebugResources-4\:/layout/dialog_add_material.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_add_material.xml
Manaknight.app-packageDebugResources-4\:/layout/dialog_add_square.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_add_square.xml
Manaknight.app-packageDebugResources-4\:/layout/dialog_alert_view.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_alert_view.xml
Manaknight.app-packageDebugResources-4\:/layout/dialog_forgetpassword.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_forgetpassword.xml
Manaknight.app-packageDebugResources-4\:/layout/dialog_resetpassword.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_resetpassword.xml
Manaknight.app-packageDebugResources-4\:/layout/fragment_accountview.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_accountview.xml
Manaknight.app-packageDebugResources-4\:/layout/fragment_add_line_items.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_add_line_items.xml
Manaknight.app-packageDebugResources-4\:/layout/fragment_alerts.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_alerts.xml
Manaknight.app-packageDebugResources-4\:/layout/fragment_companysetup.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_companysetup.xml
Manaknight.app-packageDebugResources-4\:/layout/fragment_completesetup.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_completesetup.xml
Manaknight.app-packageDebugResources-4\:/layout/fragment_costview.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_costview.xml
Manaknight.app-packageDebugResources-4\:/layout/fragment_create_customer.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_create_customer.xml
Manaknight.app-packageDebugResources-4\:/layout/fragment_create_estimation.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_create_estimation.xml
Manaknight.app-packageDebugResources-4\:/layout/fragment_dashboardview.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_dashboardview.xml
Manaknight.app-packageDebugResources-4\:/layout/fragment_draws.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_draws.xml
Manaknight.app-packageDebugResources-4\:/layout/fragment_forget_password.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_forget_password.xml
Manaknight.app-packageDebugResources-4\:/layout/fragment_friend_list.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_friend_list.xml
Manaknight.app-packageDebugResources-4\:/layout/fragment_home.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_home.xml
Manaknight.app-packageDebugResources-4\:/layout/fragment_labortrackingview.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_labortrackingview.xml
Manaknight.app-packageDebugResources-4\:/layout/fragment_line_items.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_line_items.xml
Manaknight.app-packageDebugResources-4\:/layout/fragment_linealsetup.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_linealsetup.xml
Manaknight.app-packageDebugResources-4\:/layout/fragment_linear_line_item.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_linear_line_item.xml
Manaknight.app-packageDebugResources-4\:/layout/fragment_login.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_login.xml
Manaknight.app-packageDebugResources-4\:/layout/fragment_material_line_item.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_material_line_item.xml
Manaknight.app-packageDebugResources-4\:/layout/fragment_materialsetup.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_materialsetup.xml
Manaknight.app-packageDebugResources-4\:/layout/fragment_profile.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_profile.xml
Manaknight.app-packageDebugResources-4\:/layout/fragment_profile_edit.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_profile_edit.xml
Manaknight.app-packageDebugResources-4\:/layout/fragment_profileview.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_profileview.xml
Manaknight.app-packageDebugResources-4\:/layout/fragment_projectview.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_projectview.xml
Manaknight.app-packageDebugResources-4\:/layout/fragment_reset_password.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_reset_password.xml
Manaknight.app-packageDebugResources-4\:/layout/fragment_room_list.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_room_list.xml
Manaknight.app-packageDebugResources-4\:/layout/fragment_sign_up.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_sign_up.xml
Manaknight.app-packageDebugResources-4\:/layout/fragment_splash.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_splash.xml
Manaknight.app-packageDebugResources-4\:/layout/fragment_squresetup.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_squresetup.xml
Manaknight.app-packageDebugResources-4\:/layout/fragment_subscription.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_subscription.xml
Manaknight.app-packageDebugResources-4\:/layout/fragment_trackingview.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_trackingview.xml
Manaknight.app-packageDebugResources-4\:/layout/fragment_workerview.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_workerview.xml
Manaknight.app-packageDebugResources-4\:/layout/header.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\header.xml
Manaknight.app-packageDebugResources-4\:/layout/item_app_alert.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_app_alert.xml
Manaknight.app-packageDebugResources-4\:/layout/item_broadcast_video.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_broadcast_video.xml
Manaknight.app-packageDebugResources-4\:/layout/item_customer.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_customer.xml
Manaknight.app-packageDebugResources-4\:/layout/item_dashboard_project.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_dashboard_project.xml
Manaknight.app-packageDebugResources-4\:/layout/item_draw.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_draw.xml
Manaknight.app-packageDebugResources-4\:/layout/item_employee.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_employee.xml
Manaknight.app-packageDebugResources-4\:/layout/item_filter_option.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_filter_option.xml
Manaknight.app-packageDebugResources-4\:/layout/item_line.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_line.xml
Manaknight.app-packageDebugResources-4\:/layout/item_line_lineal.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_line_lineal.xml
Manaknight.app-packageDebugResources-4\:/layout/item_line_material.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_line_material.xml
Manaknight.app-packageDebugResources-4\:/layout/item_line_total.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_line_total.xml
Manaknight.app-packageDebugResources-4\:/layout/item_lineal.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_lineal.xml
Manaknight.app-packageDebugResources-4\:/layout/item_material.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_material.xml
Manaknight.app-packageDebugResources-4\:/layout/item_multi_select_filter_option.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_multi_select_filter_option.xml
Manaknight.app-packageDebugResources-4\:/layout/progress_dialog.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\progress_dialog.xml
Manaknight.app-packageDebugResources-4\:/layout/receive_image_message_item.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\receive_image_message_item.xml
Manaknight.app-packageDebugResources-4\:/layout/receive_text_message_item.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\receive_text_message_item.xml
Manaknight.app-packageDebugResources-4\:/layout/receive_video_message_item.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\receive_video_message_item.xml
Manaknight.app-packageDebugResources-4\:/layout/room_data.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\room_data.xml
Manaknight.app-packageDebugResources-4\:/layout/send_image_message_item.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\send_image_message_item.xml
Manaknight.app-packageDebugResources-4\:/layout/send_text_message_item.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\send_text_message_item.xml
Manaknight.app-packageDebugResources-4\:/layout/send_video_message_item.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\send_video_message_item.xml
Manaknight.app-packageDebugResources-4\:/layout/simple_chat_view_widget.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\simple_chat_view_widget.xml
Manaknight.app-packageDebugResources-4\:/layout/user_data.xml=C\:\\Users\\OmiD\\AndroidStudioProjects\\profitpro_android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\user_data.xml
