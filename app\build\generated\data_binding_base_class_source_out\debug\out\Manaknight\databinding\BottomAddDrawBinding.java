// Generated by view binder compiler. Do not edit!
package Manaknight.databinding;

import Manaknight.R;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class BottomAddDrawBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView addHeading;

  @NonNull
  public final ImageView backButton;

  @NonNull
  public final MaterialButton btnSave;

  @NonNull
  public final EditText descInput;

  @NonNull
  public final EditText edTxtPercentage;

  @NonNull
  public final EditText edTxtPrice;

  @NonNull
  public final LinearLayout header;

  @NonNull
  public final LinearLayout line1;

  @NonNull
  public final ScrollView mainLayout;

  @NonNull
  public final LinearLayout percentageLayout;

  @NonNull
  public final LinearLayout priceDrawLayout;

  @NonNull
  public final LinearLayout priceLayout;

  @NonNull
  public final RadioButton radioButtonDollar;

  @NonNull
  public final RadioButton radioButtonPercentage;

  @NonNull
  public final TextView tvPrice;

  private BottomAddDrawBinding(@NonNull LinearLayout rootView, @NonNull TextView addHeading,
      @NonNull ImageView backButton, @NonNull MaterialButton btnSave, @NonNull EditText descInput,
      @NonNull EditText edTxtPercentage, @NonNull EditText edTxtPrice, @NonNull LinearLayout header,
      @NonNull LinearLayout line1, @NonNull ScrollView mainLayout,
      @NonNull LinearLayout percentageLayout, @NonNull LinearLayout priceDrawLayout,
      @NonNull LinearLayout priceLayout, @NonNull RadioButton radioButtonDollar,
      @NonNull RadioButton radioButtonPercentage, @NonNull TextView tvPrice) {
    this.rootView = rootView;
    this.addHeading = addHeading;
    this.backButton = backButton;
    this.btnSave = btnSave;
    this.descInput = descInput;
    this.edTxtPercentage = edTxtPercentage;
    this.edTxtPrice = edTxtPrice;
    this.header = header;
    this.line1 = line1;
    this.mainLayout = mainLayout;
    this.percentageLayout = percentageLayout;
    this.priceDrawLayout = priceDrawLayout;
    this.priceLayout = priceLayout;
    this.radioButtonDollar = radioButtonDollar;
    this.radioButtonPercentage = radioButtonPercentage;
    this.tvPrice = tvPrice;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static BottomAddDrawBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static BottomAddDrawBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.bottom_add_draw, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static BottomAddDrawBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.addHeading;
      TextView addHeading = ViewBindings.findChildViewById(rootView, id);
      if (addHeading == null) {
        break missingId;
      }

      id = R.id.backButton;
      ImageView backButton = ViewBindings.findChildViewById(rootView, id);
      if (backButton == null) {
        break missingId;
      }

      id = R.id.btnSave;
      MaterialButton btnSave = ViewBindings.findChildViewById(rootView, id);
      if (btnSave == null) {
        break missingId;
      }

      id = R.id.descInput;
      EditText descInput = ViewBindings.findChildViewById(rootView, id);
      if (descInput == null) {
        break missingId;
      }

      id = R.id.edTxtPercentage;
      EditText edTxtPercentage = ViewBindings.findChildViewById(rootView, id);
      if (edTxtPercentage == null) {
        break missingId;
      }

      id = R.id.edTxtPrice;
      EditText edTxtPrice = ViewBindings.findChildViewById(rootView, id);
      if (edTxtPrice == null) {
        break missingId;
      }

      id = R.id.header;
      LinearLayout header = ViewBindings.findChildViewById(rootView, id);
      if (header == null) {
        break missingId;
      }

      id = R.id.line1;
      LinearLayout line1 = ViewBindings.findChildViewById(rootView, id);
      if (line1 == null) {
        break missingId;
      }

      id = R.id.mainLayout;
      ScrollView mainLayout = ViewBindings.findChildViewById(rootView, id);
      if (mainLayout == null) {
        break missingId;
      }

      id = R.id.percentageLayout;
      LinearLayout percentageLayout = ViewBindings.findChildViewById(rootView, id);
      if (percentageLayout == null) {
        break missingId;
      }

      id = R.id.priceDrawLayout;
      LinearLayout priceDrawLayout = ViewBindings.findChildViewById(rootView, id);
      if (priceDrawLayout == null) {
        break missingId;
      }

      id = R.id.priceLayout;
      LinearLayout priceLayout = ViewBindings.findChildViewById(rootView, id);
      if (priceLayout == null) {
        break missingId;
      }

      id = R.id.radioButtonDollar;
      RadioButton radioButtonDollar = ViewBindings.findChildViewById(rootView, id);
      if (radioButtonDollar == null) {
        break missingId;
      }

      id = R.id.radioButtonPercentage;
      RadioButton radioButtonPercentage = ViewBindings.findChildViewById(rootView, id);
      if (radioButtonPercentage == null) {
        break missingId;
      }

      id = R.id.tvPrice;
      TextView tvPrice = ViewBindings.findChildViewById(rootView, id);
      if (tvPrice == null) {
        break missingId;
      }

      return new BottomAddDrawBinding((LinearLayout) rootView, addHeading, backButton, btnSave,
          descInput, edTxtPercentage, edTxtPrice, header, line1, mainLayout, percentageLayout,
          priceDrawLayout, priceLayout, radioButtonDollar, radioButtonPercentage, tvPrice);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
